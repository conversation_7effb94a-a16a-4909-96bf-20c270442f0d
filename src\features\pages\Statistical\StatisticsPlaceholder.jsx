import { useState, useEffect, useRef } from 'react';
import { Line, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement
} from 'chart.js';
import '../../../styles/StatisticsPlaceholder.css';
import userGroupIcon from '../../../assets/users.svg';
import fileIcon from '../../../assets/file-text.svg';
import chartIcon from '../../../assets/chart-column-decreasing.svg';
import tichIcon from '../../../assets/tich.svg';
import dropdownIcon from '../../../assets/icon-sidebar/dropdown.svg';
import legendNodeIcon from '../../../assets/LegendNode.svg';
import searchIcon from '../../../assets/search.svg';
import todayIcon from '../../../assets/today.svg';
import downloadIcon from '../../../assets/download.svg';
import eyeIcon from '../../../assets/eye.svg';
import strokeIcon from '../../../assets/stroke.svg';
import EmployeeStatisticsPopup from '../../components/EmployeeStatisticsPopup';
import { getAllProjects } from '../../../api/projectManagement';
import { getAllDepartments, transformDepartmentData } from '../../../api/departmentManagement';
import { getUsersByDepartment, transformUserData } from '../../../api/userManagement';
import { STATISTICS_ENDPOINTS, ADMIN_ENDPOINTS } from '../../../api/endpoints';

// Đăng ký các components Chart.js cần thiết
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement
);

const StatisticsPlaceholder = () => {
  const [selectedDepartment, setSelectedDepartment] = useState('Phòng IT');
  const [selectedYear, setSelectedYear] = useState('2025');
  const [openDepartmentDropdown, setOpenDepartmentDropdown] = useState(false);
  const [openYearDropdown, setOpenYearDropdown] = useState(false);
  const [showUserPopup, setShowUserPopup] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Table filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTableProject, setSelectedTableProject] = useState('Tất cả dự án');
  const [selectedStatus, setSelectedStatus] = useState('Tất cả trạng thái');
  const [selectedDateRange, setSelectedDateRange] = useState({ start: null, end: null });
  const [isTableProjectOpen, setIsTableProjectOpen] = useState(false);
  const [isStatusOpen, setIsStatusOpen] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Date picker states
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [selectingStart, setSelectingStart] = useState(true);
  const [isMonthSelectOpen, setIsMonthSelectOpen] = useState(false);
  const [isYearSelectOpen, setIsYearSelectOpen] = useState(false);

  // Projects state
  const [allProjects, setAllProjects] = useState([]);
  const [projectsLoading, setProjectsLoading] = useState(true);

  // Departments state
  const [departments, setDepartments] = useState([]);
  const [departmentsLoading, setDepartmentsLoading] = useState(true);

  // Statistics state
  const [statisticsData, setStatisticsData] = useState({
    users: 0,
    projects: 0,
    tasks: 0,
    completionRate: 0
  });
  const [statisticsLoading, setStatisticsLoading] = useState(true);

  // User table state
  const [userData, setUserData] = useState([]);
  const [userLoading, setUserLoading] = useState(true);
  const [currentUserPage, setCurrentUserPage] = useState(1);
  const USERS_PER_PAGE = 5;



  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.statistics-placeholder-dropdown') && !event.target.closest('.statistics-placeholder-year-dropdown')) {
        setOpenDepartmentDropdown(false);
        setOpenYearDropdown(false);
      }
      if (!event.target.closest('.procedure-dropdown')) {
        setIsTableProjectOpen(false);
        setIsStatusOpen(false);
      }
      if (!event.target.closest('.date-picker-container') && !event.target.closest('.filter-date')) {
        setIsDatePickerOpen(false);
        setIsMonthSelectOpen(false);
        setIsYearSelectOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      setProjectsLoading(true);
      try {
        const response = await getAllProjects();
        const projects = response.data || [];
        setAllProjects(projects);
      } catch (err) {
        console.warn('Error fetching projects:', err);
        setAllProjects([]);
      } finally {
        setProjectsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Fetch departments from API
  useEffect(() => {
    const fetchDepartments = async () => {
      setDepartmentsLoading(true);
      try {
        const response = await getAllDepartments({ includeInactive: true });
        if (response.success) {
          const transformedDepartments = (response.data || []).map(transformDepartmentData);
          setDepartments(transformedDepartments);
          // Set first department as default if no department is selected
          if (transformedDepartments.length > 0 && selectedDepartment === 'Phòng IT') {
            setSelectedDepartment(transformedDepartments[0].name);
          }
        }
      } catch (error) {
        console.error('Error fetching departments:', error);
        setDepartments([]);
      } finally {
        setDepartmentsLoading(false);
      }
    };

    fetchDepartments();
  }, [selectedDepartment]);

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('token') ||
                  localStorage.getItem('authToken') ||
                  JSON.parse(localStorage.getItem('user') || '{}').token;
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  };

  // Helper function to handle API response
  const handleApiResponse = async (response) => {
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    return response.json();
  };

  // Fetch projects by department from API
  const fetchProjectsByDepartment = async (departmentId) => {
    try {
      const response = await fetch(`${ADMIN_ENDPOINTS.ALL_PROJECTS}?departmentId=${departmentId}`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });
      const result = await handleApiResponse(response);
      return result.success ? (result.data || []) : [];
    } catch (error) {
      console.error('Error fetching projects by department:', error);
      return [];
    }
  };

  // Fetch tasks for projects
  const fetchTasksForProjects = async (projects) => {
    try {
      let allTasks = [];
      let completedTasks = 0;

      for (const project of projects) {
        try {
          const response = await fetch(ADMIN_ENDPOINTS.PROJECT_TASKS(project._id || project.id), {
            method: 'GET',
            headers: getAuthHeaders(),
          });
          const result = await handleApiResponse(response);

          if (result.success && result.data) {
            const projectTasks = result.data || [];
            allTasks = [...allTasks, ...projectTasks];

            // Count completed tasks
            const completed = projectTasks.filter(task =>
              task.status === 'completed' || task.status === 'done'
            ).length;
            completedTasks += completed;
          }
        } catch (error) {
          console.error(`Error fetching tasks for project ${project._id || project.id}:`, error);
          // Skip this project if tasks cannot be fetched
        }
      }

      return {
        totalTasks: allTasks.length,
        completedTasks: completedTasks,
        completionRate: allTasks.length > 0 ? Math.round((completedTasks / allTasks.length) * 100) : 0
      };
    } catch (error) {
      console.error('Error fetching tasks:', error);
      return { totalTasks: 0, completedTasks: 0, completionRate: 0 };
    }
  };

  // Fetch statistics data for selected department
  const fetchStatisticsData = async (departmentName) => {
    if (!departmentName || departmentsLoading) return;

    setStatisticsLoading(true);
    try {
      // Find department by name to get ID
      const selectedDept = departments.find(dept => dept.name === departmentName);
      if (!selectedDept) {
        console.warn('Department not found:', departmentName);
        setStatisticsLoading(false);
        return;
      }

      console.log(`Fetching statistics for department: ${departmentName} (ID: ${selectedDept.id})`);

      // Fetch all data in parallel for better performance
      const [usersResponse, departmentProjects] = await Promise.all([
        getUsersByDepartment(selectedDept.id).catch(err => {
          console.error('Error fetching users:', err);
          return { success: false, data: [] };
        }),
        fetchProjectsByDepartment(selectedDept.id).catch(err => {
          console.error('Error fetching projects:', err);
          return [];
        })
      ]);

      // Get user count
      const userCount = usersResponse.success ? (usersResponse.data || []).length : 0;

      // Get project count
      const projectCount = departmentProjects.length;

      // Fetch tasks data for all projects
      const tasksData = await fetchTasksForProjects(departmentProjects);

      console.log(`Statistics for ${departmentName}:`, {
        users: userCount,
        projects: projectCount,
        tasks: tasksData.totalTasks,
        completionRate: tasksData.completionRate
      });

      setStatisticsData({
        users: userCount,
        projects: projectCount,
        tasks: tasksData.totalTasks,
        completionRate: tasksData.completionRate
      });

    } catch (error) {
      console.error('Error fetching statistics data:', error);
      // Set empty data on error
      setStatisticsData({
        users: 0,
        projects: 0,
        tasks: 0,
        completionRate: 0
      });
    } finally {
      setStatisticsLoading(false);
    }
  };

  // Fetch user data for selected department
  const fetchUserData = async (departmentName) => {
    if (!departmentName || departmentsLoading) return;

    setUserLoading(true);
    try {
      // Find department by name to get ID
      const selectedDept = departments.find(dept => dept.name === departmentName);
      if (!selectedDept) {
        console.warn('Department not found for user data:', departmentName);
        setUserLoading(false);
        return;
      }

      console.log(`Fetching user data for department: ${departmentName} (ID: ${selectedDept.id})`);

      // Fetch users in this department
      const usersResponse = await getUsersByDepartment(selectedDept.id, {
        populate: 'department,departmentId',
        include: 'department'
      });

      if (!usersResponse.success) {
        console.error('Failed to fetch users:', usersResponse);
        setUserData([]);
        return;
      }

      const users = usersResponse.data || [];
      console.log(`Found ${users.length} users in ${departmentName}`);

      // Transform and enrich user data with statistics
      const enrichedUsers = await Promise.all(
        users.map(async (user) => {
          try {
            // Transform basic user data
            const transformedUser = transformUserData(user);

            // Fetch user's projects and tasks statistics
            const userStats = await fetchUserStatistics(transformedUser.id);

            return {
              id: transformedUser.id,
              name: transformedUser.name,
              position: transformedUser.department,
              avatar: transformedUser.avatar,
              email: transformedUser.email,
              role: transformedUser.role,
              createdAt: transformedUser.createdAt,
              totalProjects: userStats.totalProjects,
              totalTasks: userStats.totalTasks,
              completionRate: userStats.completionRate,
              status: userStats.status,
              statusData: userStats.statusData,
              projects: userStats.projects
            };
          } catch (error) {
            console.error(`Error processing user ${user._id || user.id}:`, error);
            // Return basic user data with default stats
            const transformedUser = transformUserData(user);
            return {
              id: transformedUser.id,
              name: transformedUser.name,
              position: transformedUser.department,
              avatar: transformedUser.avatar,
              email: transformedUser.email,
              role: transformedUser.role,
              createdAt: transformedUser.createdAt,
              totalProjects: 0,
              totalTasks: 0,
              completionRate: 0,
              status: 'Chưa phân việc',
              statusData: {
                labels: ['Xem xét', 'Hoàn thành đúng hạn', 'Hoàn thành muộn', 'Quá hạn', 'Hoàn thành sớm', 'Đang chờ', 'Đang triển khai'],
                data: [0, 0, 0, 0, 0, 0, 0],
                colors: ['#BA68C8', '#26C6DA', '#FFB74D', '#EF5350', '#66BB6A', '#B0BEC5', '#42A5F5']
              },
              projects: []
            };
          }
        })
      );

      setUserData(enrichedUsers);
      console.log(`Successfully loaded ${enrichedUsers.length} users with statistics`);

    } catch (error) {
      console.error('Error fetching user data:', error);
      setUserData([]);
    } finally {
      setUserLoading(false);
    }
  };

  // Fetch user statistics (projects, tasks, completion rate)
  const fetchUserStatistics = async (userId) => {
    try {
      // Fetch user's projects (as member or leader)
      const userProjectsResponse = await fetch(`${ADMIN_ENDPOINTS.ALL_PROJECTS}?userId=${userId}`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      let userProjects = [];
      if (userProjectsResponse.ok) {
        const result = await userProjectsResponse.json();
        userProjects = result.success ? (result.data || []) : [];
      }

      // Fetch user's tasks from all projects
      let allUserTasks = [];
      for (const project of userProjects) {
        try {
          const tasksResponse = await fetch(ADMIN_ENDPOINTS.PROJECT_TASKS(project._id || project.id), {
            method: 'GET',
            headers: getAuthHeaders(),
          });

          if (tasksResponse.ok) {
            const tasksResult = await tasksResponse.json();
            if (tasksResult.success && tasksResult.data) {
              // Filter tasks assigned to this user
              const userTasks = (tasksResult.data || []).filter(task =>
                task.assignedTo === userId ||
                (task.assignedTo && task.assignedTo._id === userId) ||
                (task.assignedTo && task.assignedTo.id === userId)
              );
              allUserTasks = [...allUserTasks, ...userTasks];
            }
          }
        } catch (error) {
          console.error(`Error fetching tasks for project ${project._id || project.id}:`, error);
        }
      }

      // Calculate statistics
      const totalProjects = userProjects.length;
      const totalTasks = allUserTasks.length;

      // Count tasks by status
      const statusCounts = {
        'Xem xét': 0,
        'Hoàn thành đúng hạn': 0,
        'Hoàn thành muộn': 0,
        'Quá hạn': 0,
        'Hoàn thành sớm': 0,
        'Đang chờ': 0,
        'Đang triển khai': 0
      };

      let completedTasks = 0;

      allUserTasks.forEach(task => {
        const status = task.status?.toLowerCase() || 'pending';

        if (status === 'completed' || status === 'done') {
          completedTasks++;
          // Determine if completed early, on time, or late based on dates
          if (task.completedAt && task.dueDate) {
            const completedDate = new Date(task.completedAt);
            const dueDate = new Date(task.dueDate);
            if (completedDate < dueDate) {
              statusCounts['Hoàn thành sớm']++;
            } else if (completedDate.toDateString() === dueDate.toDateString()) {
              statusCounts['Hoàn thành đúng hạn']++;
            } else {
              statusCounts['Hoàn thành muộn']++;
            }
          } else {
            statusCounts['Hoàn thành đúng hạn']++;
          }
        } else if (status === 'in_progress' || status === 'inprogress') {
          statusCounts['Đang triển khai']++;
        } else if (status === 'pending' || status === 'waiting') {
          statusCounts['Đang chờ']++;
        } else if (status === 'review') {
          statusCounts['Xem xét']++;
        } else if (status === 'overdue') {
          statusCounts['Quá hạn']++;
        } else {
          statusCounts['Đang chờ']++;
        }
      });

      const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

      // Determine overall status
      let overallStatus = 'Chưa phân việc';
      let chartData = {
        labels: ['Xem xét', 'Hoàn thành đúng hạn', 'Hoàn thành muộn', 'Quá hạn', 'Hoàn thành sớm', 'Đang chờ', 'Đang triển khai'],
        data: [0, 0, 0, 0, 0, 0, 0],
        colors: ['#BA68C8', '#26C6DA', '#FFB74D', '#EF5350', '#66BB6A', '#B0BEC5', '#42A5F5']
      };

      if (totalTasks > 0) {
        if (statusCounts['Quá hạn'] > 0) {
          overallStatus = 'Quá hạn';
        } else if (statusCounts['Đang triển khai'] > 0) {
          overallStatus = 'Đang triển khai';
        } else if (statusCounts['Đang chờ'] > 0) {
          overallStatus = 'Đang chờ';
        } else if (completedTasks > 0) {
          overallStatus = 'Hoàn thành';
        } else {
          overallStatus = 'Xem xét';
        }

        // Show all status counts in chart (including zeros for legend)
        const colorMap = {
          'Xem xét': '#BA68C8',
          'Hoàn thành đúng hạn': '#26C6DA',
          'Hoàn thành muộn': '#FFB74D',
          'Quá hạn': '#EF5350',
          'Hoàn thành sớm': '#66BB6A',
          'Đang chờ': '#B0BEC5',
          'Đang triển khai': '#42A5F5'
        };

        const allLabels = Object.keys(statusCounts);
        const allData = Object.values(statusCounts);
        const allColors = allLabels.map(status => colorMap[status]);

        chartData = {
          labels: allLabels,
          data: allData,
          colors: allColors
        };
      }

      return {
        totalProjects,
        totalTasks,
        completionRate,
        status: overallStatus,
        statusData: chartData,
        projects: userProjects // Thêm projects data để hiển thị trong popup
      };

    } catch (error) {
      console.error('Error fetching user statistics:', error);
      return {
        totalProjects: 0,
        totalTasks: 0,
        completionRate: 0,
        status: 'Chưa phân việc',
        statusData: {
          labels: ['Xem xét', 'Hoàn thành đúng hạn', 'Hoàn thành muộn', 'Quá hạn', 'Hoàn thành sớm', 'Đang chờ', 'Đang triển khai'],
          data: [0, 0, 0, 0, 0, 0, 0],
          colors: ['#BA68C8', '#26C6DA', '#FFB74D', '#EF5350', '#66BB6A', '#B0BEC5', '#42A5F5']
        },
        projects: []
      };
    }
  };



  // Fetch statistics when department changes
  useEffect(() => {
    if (selectedDepartment && departments.length > 0 && !departmentsLoading) {
      fetchStatisticsData(selectedDepartment);
      fetchUserData(selectedDepartment);
    }
  }, [selectedDepartment, departments, departmentsLoading]);

  // Date picker helper functions
  const monthNames = [
    "Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6",
    "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"
  ];

  const weekdays = ["Th2", "Th3", "Th4", "Th5", "Th6", "Th7", "CN"];

  const getDaysInMonth = (month, year) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (month, year) => {
    const firstDay = new Date(year, month, 1).getDay();
    return firstDay === 0 ? 6 : firstDay - 1; // Convert Sunday (0) to 6, Monday (1) to 0, etc.
  };

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth, currentYear);
    const firstDay = getFirstDayOfMonth(currentMonth, currentYear);
    const days = [];

    // Previous month days
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const daysInPrevMonth = getDaysInMonth(prevMonth, prevYear);

    for (let i = firstDay - 1; i >= 0; i--) {
      days.push({
        day: daysInPrevMonth - i,
        currentMonth: false,
        date: new Date(prevYear, prevMonth, daysInPrevMonth - i)
      });
    }

    // Current month days
    for (let day = 1; day <= daysInMonth; day++) {
      days.push({
        day,
        currentMonth: true,
        date: new Date(currentYear, currentMonth, day)
      });
    }

    // Next month days
    const remainingDays = 42 - days.length; // 6 rows × 7 days
    const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
    const nextYear = currentMonth === 11 ? currentYear + 1 : currentYear;

    for (let day = 1; day <= remainingDays; day++) {
      days.push({
        day,
        currentMonth: false,
        date: new Date(nextYear, nextMonth, day)
      });
    }

    return days;
  };

  const navigateMonth = (direction) => {
    if (direction === -1) {
      if (currentMonth === 0) {
        setCurrentMonth(11);
        setCurrentYear(currentYear - 1);
      } else {
        setCurrentMonth(currentMonth - 1);
      }
    } else {
      if (currentMonth === 11) {
        setCurrentMonth(0);
        setCurrentYear(currentYear + 1);
      } else {
        setCurrentMonth(currentMonth + 1);
      }
    }
  };

  const selectDate = (date) => {
    if (selectingStart || !selectedDateRange.start) {
      setSelectedDateRange({ start: date, end: null });
      setSelectingStart(false);
    } else {
      if (date < selectedDateRange.start) {
        setSelectedDateRange({ start: date, end: selectedDateRange.start });
      } else {
        setSelectedDateRange({ ...selectedDateRange, end: date });
      }
      setSelectingStart(true);
      setIsDatePickerOpen(false);
    }
  };

  const isDateInRange = (date) => {
    if (!selectedDateRange.start || !selectedDateRange.end) return false;
    return date >= selectedDateRange.start && date <= selectedDateRange.end;
  };

  const isDateSelected = (date) => {
    if (!selectedDateRange.start) return false;
    if (selectedDateRange.start.getTime() === date.getTime()) return true;
    if (selectedDateRange.end && selectedDateRange.end.getTime() === date.getTime()) return true;
    return false;
  };

  const formatDateRange = () => {
    if (!selectedDateRange.start) return "Chọn mốc thời gian";
    if (!selectedDateRange.end) {
      return `${selectedDateRange.start.getDate()}/${selectedDateRange.start.getMonth() + 1}/${selectedDateRange.start.getFullYear()} - ...`;
    }
    return `${selectedDateRange.start.getDate()}/${selectedDateRange.start.getMonth() + 1}/${selectedDateRange.start.getFullYear()} - ${selectedDateRange.end.getDate()}/${selectedDateRange.end.getMonth() + 1}/${selectedDateRange.end.getFullYear()}`;
  };

  // Month/Year dropdown functions
  const toggleMonthSelect = (e) => {
    e.stopPropagation();
    setIsMonthSelectOpen(!isMonthSelectOpen);
    setIsYearSelectOpen(false);
  };

  const toggleYearSelect = (e) => {
    e.stopPropagation();
    setIsYearSelectOpen(!isYearSelectOpen);
    setIsMonthSelectOpen(false);
  };

  const selectMonth = (monthIndex) => {
    setCurrentMonth(monthIndex);
    setIsMonthSelectOpen(false);
  };

  const selectYear = (year) => {
    setCurrentYear(year);
    setIsYearSelectOpen(false);
  };

  // Generate years range (from current year - 5 to current year + 5)
  const currentYearValue = new Date().getFullYear();
  const years = Array.from({ length: 11 }, (_, i) => currentYearValue - 5 + i);

  // Tạo gradient cho background
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const gradient = ctx.createLinearGradient(0, 0, 0, 400);
  gradient.addColorStop(0, 'rgba(112, 134, 253, 0.3)');
  gradient.addColorStop(1, 'rgba(112, 134, 253, 0.05)');

  // Mock data cho biểu đồ line chart - giữ lại để test giao diện
  const chartData = {
    labels: ['01-2025', '02-2025', '03-2025', '04-2025', '05-2025', '06-2025',
             '07-2025', '08-2025', '09-2025', '10-2025', '11-2025', '12-2025'],
    datasets: [
      {
        label: 'Tỉ lệ hoàn thành',
        data: [70, 18, 32, 20, 41, 11, 57, 15, 56, 52, 91, 42],
        borderColor: '#7086FD',
        backgroundColor: gradient,
        borderWidth: 1,
        fill: true,
        tension: 0,
        pointBackgroundColor: "#7086FD",
        pointBorderColor: "#FFFFFF",
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
        pointHoverBackgroundColor: "#7086FD",
        pointHoverBorderColor: "#FFFFFF",
        pointHoverBorderWidth: 2,
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#fff',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: '#e0e0e0',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context) {
            return context.parsed.y + '%';
          }
        }
      },
    },
    scales: {
      x: {
        offset: true,
        grid: {
          color: '#e5e5e5',
          drawBorder: false,
          lineWidth: 1,
        },
        ticks: {
          color: '#666',
          font: {
            size: 11,
          },
          maxRotation: 0,
        },
      },
      y: {
        beginAtZero: true,
        max: 100,
        grid: {
          color: '#e5e5e5',
          drawBorder: false,
          lineWidth: 1,
        },
        ticks: {
          color: '#666',
          font: {
            size: 11,
          },
          stepSize: 20,
          callback: function(value) {
            return value;
          },
        },
      },
    },
    elements: {
      point: {
        hoverBackgroundColor: 'rgba(54, 162, 235, 1)',
        hoverBorderColor: 'rgba(54, 162, 235, 1)',
      },
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
  };


  
  // Table options - use fetched projects
  const projectOptions = ['Tất cả dự án', ...allProjects.map(project => project.name).filter(Boolean)];
  const statusOptions = ['Tất cả trạng thái', 'Hoàn thành sớm', 'Hoàn thành đúng hạn', 'Hoàn thành muộn', 'Đang chờ', 'Đang triển khai', 'Xem xét', 'Quá hạn'];



  // Lọc user theo searchTerm (không phân biệt hoa thường)
  const filteredUsers = userData.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const totalUsers = filteredUsers.length;
  const totalUserPages = Math.ceil(totalUsers / USERS_PER_PAGE);
  const startIndex = (currentUserPage - 1) * USERS_PER_PAGE;
  const endIndex = startIndex + USERS_PER_PAGE;
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

  // Reset page when search changes
  useEffect(() => {
    setCurrentUserPage(1);
  }, [searchTerm, selectedDepartment]);

  // Handle user popup
  const handleUserClick = (user) => {
    setSelectedUser(user);
    setShowUserPopup(true);
  };

  const handleCloseUserPopup = () => {
    setShowUserPopup(false);
    setSelectedUser(null);
  };

  // Export to Excel function
  const handleExport = () => {
    if (userLoading) {
      alert('Dữ liệu đang được tải, vui lòng đợi...');
      return;
    }

    if (userData.length === 0) {
      alert('Không có dữ liệu để xuất');
      return;
    }

    const exportData = filteredUsers.map((user, index) => ({
      "STT": index + 1,
      "Nhân viên": user.name,
      "Email": user.email || '',
      "Phòng ban": user.position,
      "Vai trò": user.role || '',
      "Tổng số dự án": user.totalProjects,
      "Tổng số nhiệm vụ": user.totalTasks,
      "Tỉ lệ hoàn thành": `${user.completionRate}%`,
      "Trạng thái hiện tại": user.status
    }));

    // Create CSV content
    const headers = Object.keys(exportData[0]);
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    // Download CSV file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `thong-ke-nhan-vien-${selectedDepartment.replace(/\s+/g, '-')}-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log(`Exported ${exportData.length} users from ${selectedDepartment}`);
  };

  // Use real statistics data instead of mock data
  const currentStats = statisticsLoading ? {
    users: 0,
    projects: 0,
    tasks: 0,
    completionRate: 0
  } : statisticsData;

  // Mini Pie Chart Component
  const MiniPieChart = ({ data }) => {
    const chartRef = useRef();
    const [tooltip, setTooltip] = useState({ display: false, x: 0, y: 0, label: '', value: '', color: '', isUnassigned: false });

    // Handle case where all data is 0 - show a placeholder circle
    const hasData = data.data.some(value => value > 0);

    const chartData = {
      labels: hasData ? data.labels : ['Chưa phân việc'],
      datasets: [
        {
          data: hasData ? data.data : [1], // Show placeholder if no data
          backgroundColor: hasData ? data.colors : ['#D1D5DB'], // Gray color different from "Xem xét"
          borderWidth: 0,
        }
      ]
    };

    const options = {
      responsive: true,
      maintainAspectRatio: false,
      rotation: 70,
      plugins: {
        legend: { display: false },
        tooltip: {
          enabled: false, // Disable default tooltip
          external: (context) => {
            const tooltipModel = context.tooltip;
            if (!tooltipModel || !tooltipModel.opacity) {
              setTooltip(t => t.display ? { ...t, display: false } : t);
              return;
            }
            const chart = context.chart;
            const canvasRect = chart.canvas.getBoundingClientRect();
            const x = canvasRect.left + window.scrollX + tooltipModel.caretX;
            const y = canvasRect.top + window.scrollY + tooltipModel.caretY;
            const label = tooltipModel.dataPoints[0]?.label || '';
            const value = tooltipModel.dataPoints[0]?.parsed || '';
            const color = tooltipModel.dataPoints[0]?.element.options.backgroundColor || '#666';

            // Special handling for "Chưa phân việc" - don't show the number
            const isUnassigned = label === 'Chưa phân việc';

            setTooltip({
              display: true,
              x,
              y,
              label,
              value: isUnassigned ? '' : value, // Empty value for unassigned status
              color,
              isUnassigned
            });
          }
        }
      },
      elements: { arc: { borderWidth: 1 } }
    };

    return (
      <div style={{ width: '100px', height: '100px', position: 'relative' }}>
        <Pie ref={chartRef} data={chartData} options={options} />
        {tooltip.display && (
          <div
            style={{
              position: 'fixed',
              left: tooltip.x + 12,
              top: tooltip.y - 16,
              background: '#fff',
              color: '#222',
              border: '1px solid #e0e0e0',
              borderRadius: 8,
              boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
              padding: '8px 12px 8px 16px',
              zIndex: 9999,
              pointerEvents: 'none',
              minWidth: 100,
              fontSize: 12,
              whiteSpace: 'nowrap',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              gap: 6
            }}
          >
            {/* Mũi nhọn bên trái, căn giữa tooltip */}
            <div style={{
              position: 'absolute',
              left: -8,
              top: '50%',
              transform: 'translateY(-50%)',
              width: 0,
              height: 0,
              borderTop: '6px solid transparent',
              borderBottom: '6px solid transparent',
              borderRight: '8px solid #fff',
              filter: 'drop-shadow(-1px 0 1px #e0e0e0)'
            }} />

            {/* For "Chưa phân việc" - only show label */}
            {tooltip.isUnassigned ? (
              <span style={{ fontWeight: 600, color: '#9CA3AF' }}>{tooltip.label}</span>
            ) : (
              <>
                {/* Label trạng thái */}
                <span style={{ fontWeight: 600, marginBottom: 2 }}>{tooltip.label}</span>
                {/* Block màu + số */}
                <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                  <span style={{ display: 'inline-block', width: 16, height: 16, background: tooltip.color, borderRadius: 3, marginRight: 4 }}></span>
                  <span style={{ fontWeight: 500 }}>{tooltip.value}</span>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    );
  };

  // Legend Component
  const StatusLegend = ({ data }) => {
    // Always show full legend with all statuses (including 0 values)
    return (
      <div className="statistics-placeholder-status-legend">
        {data.labels.map((label, index) => (
          <div key={index} className="statistics-placeholder-legend-item">
            <div
              className="statistics-placeholder-legend-color"
              style={{ backgroundColor: data.colors[index] }}
            ></div>
            <span className="statistics-placeholder-legend-label">{data.data[index]}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="statistics-placeholder-container">
      {/* Header */}
      <div className="statistics-placeholder-header">
        <div className="statistics-placeholder-title-row">
          <div className="statistics-placeholder-title-with-icon">
            <img src={chartIcon} alt="chart" className="statistics-placeholder-title-icon" />
            <h1 className="statistics-placeholder-list-title">Thống kê công việc</h1>
          </div>
        </div>

        <div className="statistics-placeholder-toolbar-row">
          <div className="statistics-placeholder-toolbar-left">
            <div className={`statistics-placeholder-dropdown ${openDepartmentDropdown ? 'open' : ''}`}>
              <button
                className="statistics-placeholder-dropdown-btn"
                onClick={() => setOpenDepartmentDropdown(!openDepartmentDropdown)}
                disabled={departmentsLoading}
              >
                <span>{departmentsLoading ? 'Đang tải...' : selectedDepartment}</span>
                <img src={dropdownIcon} alt="dropdown" className="statistics-placeholder-dropdown-icon" />
              </button>
              {openDepartmentDropdown && !departmentsLoading && (
                <div className="statistics-placeholder-dropdown-menu">
                  {departments.map(dept => (
                    <div
                      key={dept.id || dept.name}
                      className="statistics-placeholder-dropdown-item"
                      onClick={() => {
                        setSelectedDepartment(dept.name);
                        setOpenDepartmentDropdown(false);
                      }}
                    >
                      {dept.name}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="statistics-placeholder-cards-wrapper">
        <div className="statistics-placeholder-cards">
          <div className="statistics-placeholder-stat-card">
            <div className="statistics-placeholder-stat-title statistics-placeholder-stat-title-flex">
              <span>Số lượng nhân sự</span>
              <img src={userGroupIcon} alt="users" className="statistics-placeholder-stat-icon" />
            </div>
            <div className="statistics-placeholder-stat-value">
              {statisticsLoading ? (
                <div style={{
                  width: '40px',
                  height: '24px',
                  background: '#f0f0f0',
                  borderRadius: '4px',
                  animation: 'pulse 1.5s ease-in-out infinite'
                }}></div>
              ) : (
                currentStats.users
              )}
            </div>
            <div className="statistics-placeholder-stat-desc">
              {statisticsLoading ? 'Đang tải...' : `Tổng nhân viên trong ${selectedDepartment}`}
            </div>
          </div>

          <div className="statistics-placeholder-stat-card">
            <div className="statistics-placeholder-stat-title statistics-placeholder-stat-title-flex">
              <span>Số lượng dự án</span>
              <img src={fileIcon} alt="projects" className="statistics-placeholder-stat-icon" />
            </div>
            <div className="statistics-placeholder-stat-value">
              {statisticsLoading ? (
                <div style={{
                  width: '40px',
                  height: '24px',
                  background: '#f0f0f0',
                  borderRadius: '4px',
                  animation: 'pulse 1.5s ease-in-out infinite'
                }}></div>
              ) : (
                currentStats.projects
              )}
            </div>
            <div className="statistics-placeholder-stat-desc">
              {statisticsLoading ? 'Đang tải...' : `Tổng dự án thuộc ${selectedDepartment}`}
            </div>
          </div>

          <div className="statistics-placeholder-stat-card">
            <div className="statistics-placeholder-stat-title statistics-placeholder-stat-title-flex">
              <span>Số lượng công việc</span>
              <img src={chartIcon} alt="tasks" className="statistics-placeholder-stat-icon" />
            </div>
            <div className="statistics-placeholder-stat-value">
              {statisticsLoading ? (
                <div style={{
                  width: '40px',
                  height: '24px',
                  background: '#f0f0f0',
                  borderRadius: '4px',
                  animation: 'pulse 1.5s ease-in-out infinite'
                }}></div>
              ) : (
                currentStats.tasks
              )}
            </div>
            <div className="statistics-placeholder-stat-desc">
              {statisticsLoading ? 'Đang tải...' : `Tổng công việc trong các dự án của ${selectedDepartment}`}
            </div>
          </div>

          <div className="statistics-placeholder-stat-card">
            <div className="statistics-placeholder-stat-title statistics-placeholder-stat-title-flex">
              <span>Tỉ lệ hoàn thành</span>
              <img src={tichIcon} alt="completion" className="statistics-placeholder-stat-icon" />
            </div>
            <div className="statistics-placeholder-stat-value">
              {statisticsLoading ? (
                <div style={{
                  width: '40px',
                  height: '24px',
                  background: '#f0f0f0',
                  borderRadius: '4px',
                  animation: 'pulse 1.5s ease-in-out infinite'
                }}></div>
              ) : (
                `${currentStats.completionRate}%`
              )}
            </div>
            <div className="statistics-placeholder-stat-desc">
              {statisticsLoading ? 'Đang tải...' : `Tỉ lệ hoàn thành công việc của ${selectedDepartment}`}
            </div>
          </div>
        </div>
      </div>

      {/* Chart Section */}
      <div className="statistics-placeholder-chart-section">
        <div className="statistics-placeholder-chart-card">
          <div className="statistics-placeholder-chart-header">
            <div className="statistics-placeholder-chart-title">
              <img src={chartIcon} alt="chart" className="statistics-placeholder-chart-title-icon" />
              <span>Tỉ lệ hoàn thành của phòng ban</span>
            </div>
            <div className="statistics-placeholder-chart-subtitle">
              Phân bố nhiệm vụ theo thời gian cho {selectedDepartment}
            </div>
          </div>

          <div className="statistics-placeholder-chart-type-label">
            <img src={strokeIcon} alt="stroke" className="statistics-placeholder-chart-type-icon" />
            Line
            <div className="statistics-placeholder-chart-control-icon"></div>
          </div>

          <div className="statistics-placeholder-chart-container">
            <Line data={chartData} options={chartOptions} />
          </div>

          {/* Year Filter Below Chart */}
          <div className="statistics-placeholder-year-filter">
            <div className={`statistics-placeholder-year-dropdown ${openYearDropdown ? 'open' : ''}`}>
              <button
                className="statistics-placeholder-year-dropdown-btn"
                onClick={() => setOpenYearDropdown(!openYearDropdown)}
              >
                <img src={legendNodeIcon} alt="legend" className="statistics-placeholder-legend-icon" />
                <span>{selectedYear}</span>
                <img src={dropdownIcon} alt="dropdown" className="statistics-placeholder-dropdown-icon" />
              </button>
              {openYearDropdown && (
                <div className="statistics-placeholder-year-dropdown-menu">
                  {years.map(year => (
                    <div
                      key={year}
                      className="statistics-placeholder-year-dropdown-item"
                      onClick={() => {
                        setSelectedYear(year);
                        setOpenYearDropdown(false);
                      }}
                    >
                      {year}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* User Statistics Table */}
      <div className="statistics-placeholder-table-section">
        <div className="statistics-placeholder-table-card">
          <div className="statistics-placeholder-table-header">
            <div className="statistics-placeholder-table-title">
              <img src={userGroupIcon} alt="users" className="statistics-placeholder-table-title-icon" />
              <span>Quản lí nhân viên</span>
            </div>
            <div className="statistics-placeholder-table-subtitle">
              Nhân viên {selectedDepartment}
            </div>
          </div>

          {/* Table Filters */}
          <div className="statistical-filters-row">
            <div className="filter-controls">
              <div className="filter-group">
                <input
                  type="text"
                  className="filter-search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Tìm kiếm nhân sự..."
                />
                <img src={searchIcon} alt="search" className="search-icon" />
              </div>

              <div className="filter-group">
                <div className="date-picker-wrapper">
                  <button
                    className="filter-date"
                    onClick={() => setIsDatePickerOpen(!isDatePickerOpen)}
                  >
                    <span>{formatDateRange()}</span>
                    <img src={todayIcon} alt="calendar" className="date-icon" />
                  </button>

                  {isDatePickerOpen && (
                    <div className="date-picker-container">
                      <div className="date-picker-header">
                        <button onClick={() => navigateMonth(-1)} className="month-nav-btn">
                          ‹
                        </button>
                        <div className="date-picker-selectors">
                          <button
                            className="month-selector-btn"
                            onClick={toggleMonthSelect}
                          >
                            {monthNames[currentMonth]}
                            <span className="dropdown-arrow">▼</span>
                          </button>
                          <button
                            className="year-selector-btn"
                            onClick={toggleYearSelect}
                          >
                            {currentYear}
                            <span className="dropdown-arrow">▼</span>
                          </button>

                          {isMonthSelectOpen && (
                            <div className="month-select-dropdown">
                              <div className="month-select-list">
                                {monthNames.map((month, index) => (
                                  <div
                                    key={index}
                                    className={`month-option ${index === currentMonth ? 'selected' : ''}`}
                                    onClick={() => selectMonth(index)}
                                  >
                                    {month}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {isYearSelectOpen && (
                            <div className="year-select-dropdown">
                              <div className="year-select-list">
                                {years.map(year => (
                                  <div
                                    key={year}
                                    className={`year-option ${year === currentYear ? 'selected' : ''}`}
                                    onClick={() => selectYear(year)}
                                  >
                                    {year}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                        <button onClick={() => navigateMonth(1)} className="month-nav-btn">
                          ›
                        </button>
                      </div>

                      <div className="date-picker-weekdays">
                        {weekdays.map(day => (
                          <div key={day} className="date-picker-weekday">{day}</div>
                        ))}
                      </div>

                      <div className="date-picker-days">
                        {generateCalendarDays().map((dateObj, index) => (
                          <div
                            key={index}
                            className={`date-picker-day ${!dateObj.currentMonth ? 'other-month' : ''}
                                        ${isDateSelected(dateObj.date) ? 'selected' : ''}
                                        ${isDateInRange(dateObj.date) ? 'in-range' : ''}`}
                            onClick={() => dateObj.currentMonth && selectDate(dateObj.date)}
                          >
                            {dateObj.day}
                          </div>
                        ))}
                      </div>

                      <div className="date-picker-actions">
                        <button
                          onClick={() => {
                            setSelectedDateRange({ start: null, end: null });
                            setSelectingStart(true);
                          }}
                          className="clear-btn"
                        >
                          Xóa
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="filter-group">
                <div className="procedure-dropdown">
                  <button
                    className="procedure-dropdown-btn"
                    onClick={() => setIsTableProjectOpen(!isTableProjectOpen)}
                    disabled={projectsLoading}
                  >
                    <span>{projectsLoading ? 'Đang tải...' : selectedTableProject}</span>
                    <img src={dropdownIcon} alt="dropdown" className="select-icon" />
                  </button>
                  {isTableProjectOpen && (
                    <div className="procedure-dropdown-menu">
                      {projectOptions.map((project) => (
                        <div
                          key={project}
                          className="procedure-dropdown-item"
                          onClick={() => {
                            setSelectedTableProject(project);
                            setIsTableProjectOpen(false);
                          }}
                        >
                          {project}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="filter-group">
                <div className="procedure-dropdown">
                  <button
                    className="procedure-dropdown-btn"
                    onClick={() => setIsStatusOpen(!isStatusOpen)}
                  >
                    <span>{selectedStatus}</span>
                    <img src={dropdownIcon} alt="dropdown" className="select-icon" />
                  </button>
                  {isStatusOpen && (
                    <div className="procedure-dropdown-menu">
                      {statusOptions.map((status) => (
                        <div
                          key={status}
                          className="procedure-dropdown-item"
                          onClick={() => {
                            setSelectedStatus(status);
                            setIsStatusOpen(false);
                          }}
                        >
                          {status}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="statistical-actions">
              <button className="btn-export" type="button" onClick={handleExport}>
                <img
                  src={downloadIcon}
                  alt="Xuất Excel"
                  style={{ width: 18, marginRight: 6 }}
                />
                Xuất Excel
              </button>
            </div>
          </div>

          {/* Table */}
          <div className="statistics-placeholder-table-container">
            <table className="statistics-placeholder-table">
              <thead>
                <tr>
                  <th className="text-left">Nhân viên</th>
                  <th>Tổng số dự án</th>
                  <th>Tổng số nhiệm vụ</th>
                  <th>Tỉ lệ hoàn thành</th>
                  <th>Tổng quan trạng thái</th>
                  <th>Hành động</th>
                </tr>
              </thead>
              <tbody>
                {userLoading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, idx) => (
                    <tr key={`loading-${idx}`} style={{ opacity: 0.7 }}>
                      <td>
                        <div className="user-info">
                          <div style={{
                            width: 40,
                            height: 40,
                            background: '#f0f0f0',
                            borderRadius: '50%',
                            animation: 'pulse 1.5s ease-in-out infinite'
                          }}></div>
                          <div className="user-details">
                            <div style={{
                              width: 120,
                              height: 16,
                              background: '#f0f0f0',
                              borderRadius: 4,
                              marginBottom: 4,
                              animation: 'pulse 1.5s ease-in-out infinite'
                            }}></div>
                            <div style={{
                              width: 80,
                              height: 14,
                              background: '#f0f0f0',
                              borderRadius: 4,
                              animation: 'pulse 1.5s ease-in-out infinite'
                            }}></div>
                          </div>
                        </div>
                      </td>
                      <td className="text-center">
                        <div style={{
                          width: 30,
                          height: 16,
                          background: '#f0f0f0',
                          borderRadius: 4,
                          margin: '0 auto',
                          animation: 'pulse 1.5s ease-in-out infinite'
                        }}></div>
                      </td>
                      <td className="text-center">
                        <div style={{
                          width: 30,
                          height: 16,
                          background: '#f0f0f0',
                          borderRadius: 4,
                          margin: '0 auto',
                          animation: 'pulse 1.5s ease-in-out infinite'
                        }}></div>
                      </td>
                      <td className="text-center">
                        <div style={{
                          width: 40,
                          height: 16,
                          background: '#f0f0f0',
                          borderRadius: 4,
                          margin: '0 auto',
                          animation: 'pulse 1.5s ease-in-out infinite'
                        }}></div>
                      </td>
                      <td className="text-center">
                        <div style={{
                          width: 100,
                          height: 100,
                          background: '#f0f0f0',
                          borderRadius: 4,
                          margin: '0 auto',
                          animation: 'pulse 1.5s ease-in-out infinite'
                        }}></div>
                      </td>
                      <td className="text-center">
                        <div style={{
                          width: 32,
                          height: 32,
                          background: '#f0f0f0',
                          borderRadius: 4,
                          margin: '0 auto',
                          animation: 'pulse 1.5s ease-in-out infinite'
                        }}></div>
                      </td>
                    </tr>
                  ))
                ) : filteredUsers.length === 0 ? (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '32px 0', color: '#888' }}>
                      <div style={{ fontSize: '48px', marginBottom: '16px', opacity: 0.3 }}>👥</div>
                      <div style={{ marginBottom: '8px', fontWeight: '500' }}>
                        {userData.length === 0 ? 'Không có nhân viên nào trong phòng ban này' : 'Không tìm thấy nhân viên nào'}
                      </div>
                      <div style={{ fontSize: '14px' }}>
                        {userData.length === 0 ? 'Phòng ban chưa có thành viên' : 'Thử điều chỉnh từ khóa tìm kiếm'}
                      </div>
                    </td>
                  </tr>
                ) : (
                  paginatedUsers.map((user) => (
                  <tr key={user.id}>
                    <td>
                      <div className="user-info">
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="user-avatar"
                          title={user.name}
                        />
                        <div className="user-details">
                          <div className="user-name">{user.name}</div>
                          <div className="user-position">{user.position}</div>
                          {user.email && (
                            <div style={{ color: '#666', fontSize: '12px' }}>{user.email}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="text-center">{user.totalProjects}</td>
                    <td className="text-center">{user.totalTasks}</td>
                    <td className="text-center">{user.completionRate}%</td>
                    <td className="text-center">
                      <div className="statistics-placeholder-status-chart">
                        <MiniPieChart data={user.statusData} />
                        <StatusLegend data={user.statusData} />
                      </div>
                    </td>
                    <td className="text-center">
                      <button
                        className="action-btn"
                        onClick={() => handleUserClick(user)}
                        title="Xem chi tiết"
                      >
                        <img src={eyeIcon} alt="view" className="action-icon" />
                      </button>
                    </td>
                  </tr>
                ))
                )}
              </tbody>
            </table>

            {/* User Pagination */}
            {totalUserPages > 1 && (
              <div className="pagination-container">
                <button
                  className={`pagination-btn${currentUserPage === 1 ? ' disabled' : ''}`}
                  onClick={() => setCurrentUserPage(p => Math.max(1, p - 1))}
                  disabled={currentUserPage === 1}
                >
                  Trước
                </button>

                {[...Array(totalUserPages)].map((_, i) => (
                  <button
                    key={i}
                    className={`pagination-btn${currentUserPage === i + 1 ? ' active' : ''}`}
                    onClick={() => setCurrentUserPage(i + 1)}
                  >
                    {i + 1}
                  </button>
                ))}

                <button
                  className={`pagination-btn${currentUserPage === totalUserPages ? ' disabled' : ''}`}
                  onClick={() => setCurrentUserPage(p => Math.min(totalUserPages, p + 1))}
                  disabled={currentUserPage === totalUserPages}
                >
                  Sau
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* User Statistics Popup */}
      {showUserPopup && selectedUser && (
        <EmployeeStatisticsPopup
          employee={selectedUser}
          onClose={handleCloseUserPopup}
        />
      )}

      {/* CSS for loading animations */}
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default StatisticsPlaceholder;
